import requests
import os
import time
import getpass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re

class WebsiteCrawler:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.downloaded_files = set()
        
    def download_captcha(self):
        """下载验证码图片"""
        try:
            # 首先访问登录页面获取验证码
            response = self.session.get(self.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找验证码图片
            captcha_img = soup.find('img', {'id': re.compile(r'captcha|verify', re.I)}) or \
                         soup.find('img', src=re.compile(r'captcha|verify|code', re.I))
            
            if captcha_img:
                captcha_url = urljoin(self.base_url, captcha_img['src'])
                captcha_response = self.session.get(captcha_url)
                
                with open('captcha.png', 'wb') as f:
                    f.write(captcha_response.content)
                print("验证码已保存为 captcha.png")
                return True
            else:
                print("未找到验证码图片")
                return False
        except Exception as e:
            print(f"下载验证码失败: {e}")
            return False
    
    def login(self):
        """登录网站"""
        print("正在获取登录页面...")
        
        # 下载验证码
        if self.download_captcha():
            print("请查看当前目录下的 captcha.png 文件")
        
        # 获取用户输入
        username = input("请输入用户名: ")
        password = getpass.getpass("请输入密码: ")
        captcha = input("请输入验证码: ")
        
        # 获取登录表单
        response = self.session.get(self.base_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找登录表单
        form = soup.find('form')
        if not form:
            print("未找到登录表单")
            return False
        
        # 构建登录数据
        login_data = {}
        for input_tag in form.find_all('input'):
            name = input_tag.get('name')
            if name:
                if input_tag.get('type') == 'password':
                    login_data[name] = password
                elif 'user' in name.lower() or 'name' in name.lower():
                    login_data[name] = username
                elif 'captcha' in name.lower() or 'verify' in name.lower():
                    login_data[name] = captcha
                else:
                    login_data[name] = input_tag.get('value', '')
        
        # 执行登录
        login_url = urljoin(self.base_url, form.get('action', ''))
        response = self.session.post(login_url, data=login_data)
        
        # 检查登录是否成功
        if '登录成功' in response.text or '欢迎' in response.text or response.url != login_url:
            print("登录成功!")
            return True
        else:
            print("登录失败，请检查用户名、密码和验证码")
            return False
    
    def save_page(self, url, content, folder='downloaded_site'):
        """保存页面内容"""
        if not os.path.exists(folder):
            os.makedirs(folder)
        
        # 生成文件名
        parsed_url = urlparse(url)
        filename = parsed_url.path.strip('/').replace('/', '_')
        if not filename or filename == '_':
            filename = 'index'
        if not filename.endswith('.html'):
            filename += '.html'
        
        filepath = os.path.join(folder, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存: {filepath}")
    
    def download_resource(self, url, folder='downloaded_site/assets'):
        """下载资源文件（CSS、JS、图片等）"""
        if url in self.downloaded_files:
            return
        
        try:
            if not os.path.exists(folder):
                os.makedirs(folder)
            
            response = self.session.get(url)
            if response.status_code == 200:
                filename = os.path.basename(urlparse(url).path)
                if not filename:
                    filename = 'resource'
                
                filepath = os.path.join(folder, filename)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                self.downloaded_files.add(url)
                print(f"已下载资源: {filename}")
        except Exception as e:
            print(f"下载资源失败 {url}: {e}")
    
    def crawl_site(self):
        """爬取网站内容"""
        if not self.login():
            return
        
        print("开始爬取网站内容...")
        visited_urls = set()
        urls_to_visit = [self.base_url]
        
        while urls_to_visit:
            current_url = urls_to_visit.pop(0)
            
            if current_url in visited_urls:
                continue
            
            try:
                print(f"正在访问: {current_url}")
                response = self.session.get(current_url)
                
                if response.status_code == 200:
                    visited_urls.add(current_url)
                    
                    # 保存页面
                    self.save_page(current_url, response.text)
                    
                    # 解析页面查找链接和资源
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 下载CSS和JS文件
                    for link in soup.find_all('link', href=True):
                        resource_url = urljoin(current_url, link['href'])
                        if resource_url.startswith(self.base_url):
                            self.download_resource(resource_url)
                    
                    for script in soup.find_all('script', src=True):
                        resource_url = urljoin(current_url, script['src'])
                        if resource_url.startswith(self.base_url):
                            self.download_resource(resource_url)
                    
                    # 下载图片
                    for img in soup.find_all('img', src=True):
                        img_url = urljoin(current_url, img['src'])
                        if img_url.startswith(self.base_url):
                            self.download_resource(img_url)
                    
                    # 查找新的页面链接
                    for link in soup.find_all('a', href=True):
                        new_url = urljoin(current_url, link['href'])
                        if (new_url.startswith(self.base_url) and 
                            new_url not in visited_urls and 
                            new_url not in urls_to_visit):
                            urls_to_visit.append(new_url)
                
                # 限速，避免对服务器造成压力
                time.sleep(2)
                
            except Exception as e:
                print(f"访问 {current_url} 时出错: {e}")
        
        print("网站爬取完成!")

def main():
    url = "http://lvytcdn.cn/admin.php"
    crawler = WebsiteCrawler(url)
    crawler.crawl_site()

if __name__ == "__main__":
    main()